<template>
  <div class="stripe-demo">
    <div class="demo-container">
      <h1 class="demo-title">Stripe 支付演示</h1>
      
      <!-- 商品信息 -->
      <div class="product-card">
        <div class="product-image">
          <img src="https://via.placeholder.com/200x200" alt="商品图片" />
        </div>
        <div class="product-info">
          <h3 class="product-name">演示商品</h3>
          <p class="product-desc">这是一个用于演示 Stripe 支付功能的测试商品</p>
          <div class="product-price">${{ demoProduct.price }}</div>
        </div>
      </div>

      <!-- 支付按钮 -->
      <div class="payment-section">
        <el-button 
          type="primary" 
          size="large" 
          @click="handleBuyNow"
          class="buy-button"
          :disabled="!stripeConfig.publishableKey || stripeConfig.publishableKey.includes('pk_test_51234567890abcdef')"
        >
          <el-icon><CreditCard /></el-icon>
          立即购买
        </el-button>
        
        <div class="payment-note" v-if="!stripeConfig.publishableKey || stripeConfig.publishableKey.includes('pk_test_51234567890abcdef')">
          <el-alert
            title="配置提醒"
            description="请在 .env 文件中配置有效的 VITE_STRIPE_PUBLISHABLE_KEY"
            type="warning"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 配置信息 -->
      <div class="config-info">
        <h3>当前配置</h3>
        <div class="config-item">
          <span class="label">Stripe 公钥：</span>
          <span class="value">{{ stripeConfig.publishableKey || '未配置' }}</span>
        </div>
        <div class="config-item">
          <span class="label">订单号：</span>
          <span class="value">{{ orderInfo.orderNumber }}</span>
        </div>
        <div class="config-item">
          <span class="label">支付金额：</span>
          <span class="value">${{ orderInfo.amount }}</span>
        </div>
      </div>

      <!-- Stripe 支付弹窗 -->
      <StripePaymentForm
        v-model:visible="stripeDialogVisible"
        :publishable-key="stripeConfig.publishableKey"
        :amount="orderInfo.amount"
        :order-number="orderInfo.orderNumber"
        :currency="orderInfo.currency"
        @success="handlePaymentSuccess"
        @error="handlePaymentError"
        @cancel="handlePaymentCancel"
      />

      <!-- 支付结果 -->
      <div class="payment-result" v-if="paymentResult">
        <el-alert
          :title="paymentResult.type === 'success' ? '支付成功' : '支付失败'"
          :description="paymentResult.message"
          :type="paymentResult.type"
          show-icon
          :closable="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { CreditCard } from '@element-plus/icons-vue'
import StripePaymentForm from '@/components/StripePaymentForm.vue'

// 演示商品
const demoProduct = reactive({
  name: '演示商品',
  price: 29.99,
  description: '这是一个用于演示 Stripe 支付功能的测试商品'
})

// 订单信息
const orderInfo = reactive({
  orderNumber: 'DEMO' + Date.now(),
  amount: demoProduct.price,
  currency: 'usd'
})

// Stripe 配置
const stripeConfig = reactive({
  publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51234567890abcdef'
})

// 弹窗控制
const stripeDialogVisible = ref(false)
const paymentResult = ref(null)

// 立即购买
const handleBuyNow = () => {
  // 重新生成订单号
  orderInfo.orderNumber = 'DEMO' + Date.now()
  paymentResult.value = null
  stripeDialogVisible.value = true
}

// 处理支付成功
const handlePaymentSuccess = (result) => {
  console.log('支付成功:', result)
  ElMessage.success('支付成功！')
  
  paymentResult.value = {
    type: 'success',
    message: `订单 ${result.orderNumber} 支付成功，Token: ${result.token}`
  }
}

// 处理支付错误
const handlePaymentError = (error) => {
  console.error('支付失败:', error)
  ElMessage.error('支付失败：' + error.message)
  
  paymentResult.value = {
    type: 'error',
    message: error.message || '支付处理失败'
  }
}

// 处理支付取消
const handlePaymentCancel = () => {
  console.log('用户取消支付')
  ElMessage.info('已取消支付')
}
</script>

<style lang="scss" scoped>
.stripe-demo {
  min-height: calc(100vh - 250px);
  padding: 3rem 5%;
  background: #f8f9fa;

  .demo-container {
    max-width: 60rem;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 1.2rem;
    padding: 3rem;
    box-shadow: 0 0.4rem 1.2rem rgba(0, 0, 0, 0.1);

    .demo-title {
      font-size: 2.4rem;
      font-weight: 600;
      color: #242426;
      text-align: center;
      margin-bottom: 3rem;
    }

    .product-card {
      display: flex;
      gap: 2rem;
      padding: 2rem;
      border: 0.1rem solid #e7e7e7;
      border-radius: 1rem;
      margin-bottom: 3rem;

      .product-image {
        flex-shrink: 0;

        img {
          width: 15rem;
          height: 15rem;
          object-fit: cover;
          border-radius: 0.8rem;
        }
      }

      .product-info {
        flex: 1;

        .product-name {
          font-size: 2rem;
          font-weight: 600;
          color: #242426;
          margin-bottom: 1rem;
        }

        .product-desc {
          font-size: 1.4rem;
          color: #606266;
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }

        .product-price {
          font-size: 2.4rem;
          font-weight: 600;
          color: #409eff;
        }
      }
    }

    .payment-section {
      text-align: center;
      margin-bottom: 3rem;

      .buy-button {
        width: 100%;
        max-width: 30rem;
        height: 5rem;
        font-size: 1.8rem;
        font-weight: 600;
        border-radius: 0.8rem;

        .el-icon {
          margin-right: 0.8rem;
          font-size: 2rem;
        }
      }

      .payment-note {
        margin-top: 2rem;
      }
    }

    .config-info {
      background: #f8f9fa;
      border-radius: 0.8rem;
      padding: 2rem;
      margin-bottom: 2rem;

      h3 {
        font-size: 1.8rem;
        font-weight: 600;
        color: #242426;
        margin-bottom: 1.5rem;
      }

      .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 1.4rem;
          color: #606266;
        }

        .value {
          font-size: 1.4rem;
          font-weight: 500;
          color: #242426;
          word-break: break-all;
          text-align: right;
          max-width: 60%;
        }
      }
    }

    .payment-result {
      margin-top: 2rem;
    }
  }
}

@media (max-width: 768px) {
  .stripe-demo {
    padding: 2rem 3%;

    .demo-container {
      padding: 2rem;

      .product-card {
        flex-direction: column;
        text-align: center;

        .product-image img {
          width: 12rem;
          height: 12rem;
        }
      }

      .config-info {
        .config-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;

          .value {
            max-width: 100%;
            text-align: left;
          }
        }
      }
    }
  }
}
</style>
