<template>
    <div class="aftersale_apply_container" v-loading="loading">
        <div class="header">
            <img src="@assets/back.png" class="back_btn" @click="goBack" />
            <h1 class="title">申请售后</h1>
        </div>

        <div v-if="productInfo" class="content">
            <!-- 产品信息 -->
            <div class="product_info">
                <div class="product_image">
                    <img :src="getProductImage(productInfo)" :alt="productInfo.name" />
                </div>
                <div class="product_details">
                    <h3 class="product_name">{{ productInfo.name || 'Ledger Flex' }}</h3>
                    <div class="product_quantity">x {{ productInfo.quantity || 1 }}</div>
                </div>
            </div>

            <!-- 售后信息表单 -->
            <div class="form_section">
                <div class="section_title">
                    <div class="title_bar"></div>
                    <span>填写售后信息</span>
                </div>

                <div class="form_group">
                    <label class="form_label">服务类型</label>
                    <div class="form_control">
                        <el-select v-model="formData.type" placeholder="请选择" class="full_width">
                            <el-option label="退货" value="return" />
                            <el-option label="换货" value="exchange" />
                            <el-option label="维修" value="repair" />
                        </el-select>
                    </div>
                </div>

                <div class="form_row">
                    <div class="form_group half">
                        <label class="form_label">退换数量 <span class="required">*</span></label>
                        <div class="form_control">
                            <el-input-number v-model="formData.quantity" :min="1" :max="productInfo.quantity"
                                class="full_width" />
                        </div>
                    </div>
                </div>

                <div class="form_group">
                    <label class="form_label">已打开包装</label>
                    <div class="form_control">
                        <el-select v-model="formData.opened" placeholder="请选择" class="full_width">
                            <el-option label="否" value="no" />
                            <el-option label="是" value="yes" />
                        </el-select>
                    </div>
                </div>

                <div class="form_group">
                    <label class="form_label">退货原因</label>
                    <div class="form_control">
                        <el-select v-model="formData.reason" placeholder="请选择" class="full_width">
                            <el-option label="未收到货" value="not_received" />
                            <el-option label="商品损坏" value="damaged" />
                            <el-option label="商品不符" value="not_match" />
                            <el-option label="质量问题" value="quality_issue" />
                            <el-option label="其他" value="other" />
                        </el-select>
                    </div>
                </div>

                <!-- 图片上传 -->
                <div class="form_group">
                    <label class="form_label">图片</label>
                    <div class="upload_area">
                        <div class="uploaded_images">
                            <div v-for="(image, index) in uploadedImages" :key="index" class="image_item">
                                <img :src="image.url" :alt="`上传图片${index + 1}`" />
                                <div class="remove_btn" @click="removeImage(index)">×</div>
                            </div>
                        </div>
                        <div class="upload_btn" @click="triggerFileUpload" v-if="uploadedImages.length < 5">
                            <div class="upload_icon">+</div>
                        </div>
                        <input ref="fileInput" type="file" accept="image/*" multiple style="display: none"
                            @change="handleFileUpload" />
                    </div>
                </div>

                <!-- 备注 -->
                <div class="form_group">
                    <label class="form_label">备注</label>
                    <div class="form_control">
                        <el-input v-model="formData.comment" type="textarea" :rows="4" placeholder="请输入"
                            class="full_width" />
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit_section">
                <button class="submit_btn" @click="submitAftersale" :disabled="submitting">
                    {{ submitting ? '提交中...' : '提交' }}
                </button>
            </div>
        </div>

        <div v-else-if="!loading" class="empty_state">
            <p>产品信息加载失败</p>
            <div class="retry_btn" @click="loadProductInfo">重新加载</div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CreateRMAPage, SubmitRMA, UploadFile } from '@api/account'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const submitting = ref(false)
const productInfo = ref({})
const uploadedImages = ref([])
const fileInput = ref(null)

const formData = ref({
    type: 'return',
    quantity: 1,
    opened: 'no',
    reason: 'not_received',
    comment: '',
    images: []
})

// 获取产品信息
const loadProductInfo = async () => {
    const orderProductId = route.query.order_product_id
    if (!orderProductId) {
        ElMessage.error('缺少产品信息')
        router.back()
        return
    }

    try {
        loading.value = true
        const response = await CreateRMAPage(orderProductId)
        console.log('产品信息:', response)

        if (response.order_product) {
            productInfo.value = response.order_product
            formData.value.quantity = 1
        }
    } catch (error) {
        console.error('获取产品信息失败:', error)
        ElMessage.error('获取产品信息失败')
    } finally {
        loading.value = false
    }
}

// 获取产品图片
const getProductImage = (product) => {
    if (product && product.image) {
        return `http://47.237.73.30/${product.image}`
    }
    return 'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'
}

// 触发文件上传
const triggerFileUpload = () => {
    fileInput.value?.click()
}

// 处理文件上传
const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files)

    for (const file of files) {
        if (uploadedImages.value.length >= 5) {
            ElMessage.warning('最多只能上传5张图片')
            break
        }

        try {
            const formData = new FormData()
            formData.append('file', file)
            formData.append('type', 'rma')

            const response = await UploadFile(formData)
            uploadedImages.value.push({
                url: response.url,
                path: response.path
            })
        } catch (error) {
            console.error('图片上传失败:', error)
            ElMessage.error('图片上传失败')
        }
    }

    // 清空文件输入
    event.target.value = ''
}

// 移除图片
const removeImage = (index) => {
    uploadedImages.value.splice(index, 1)
}

// 提交售后申请
const submitAftersale = async () => {
    if (!formData.value.type) {
        ElMessage.error('请选择服务类型')
        return
    }

    if (!formData.value.reason) {
        ElMessage.error('请选择退货原因')
        return
    }

    try {
        submitting.value = true

        const submitData = {
            order_product_id: route.query.order_product_id,
            type: formData.value.type,
            quantity: formData.value.quantity,
            opened: formData.value.opened,
            reason: formData.value.reason,
            comment: formData.value.comment,
            images: uploadedImages.value.map(img => img.path)
        }

        await SubmitRMA(submitData)
        ElMessage.success('售后申请提交成功')
        router.push('/mine/aftersale')
    } catch (error) {
        console.error('提交售后申请失败:', error)
        ElMessage.error('提交售后申请失败')
    } finally {
        submitting.value = false
    }
}

// 返回上一页
const goBack = () => {
    router.back()
}

onMounted(() => {
    loadProductInfo()
})
</script>

<style lang="scss" scoped>
@import url('./aftersaleApply.scss');
</style>
