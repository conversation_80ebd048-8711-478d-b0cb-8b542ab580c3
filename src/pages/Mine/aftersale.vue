<template>
    <div class="box" v-loading="loading">
        <h4 class="box_title">我的售后</h4>
        <div class="box_thead">
            <div class="box_thead_th w383">{{ t('center.product') }}</div>
            <div class="box_thead_th w149">{{ t('center.num') }}</div>
            <div class="box_thead_th w154">{{ t('center.type') }}</div>
            <div class="box_thead_th w202">{{ t('center.createTime1') }}</div>
            <div class="box_thead_th w92">{{ t('center.operate1') }}</div>
        </div>
        <div class="box_tbody" v-if="aftersaleList.length > 0">
            <div class="box_tr" v-for="item in aftersaleList" :key="item.id">
                <div class="box_tr_td w383">
                    <img :src="getProductImage(item.order_product)" class="box_tr_td_img"
                        :alt="item.order_product?.name" />
                    {{ item.order_product?.name || 'Ledger Flex' }}
                </div>
                <div class="box_tr_td price w149">x {{ item.quantity || 1 }}</div>
                <div class="box_tr_td price w154">{{ getTypeText(item.type) }}</div>
                <div class="box_tr_td price w202">{{ formatDate(item.created_at) }}</div>
                <div class="box_tr_td del w92" @click="viewDetail(item)">查看</div>
            </div>
        </div>
        <div v-else-if="!loading" class="empty_state">
            <p>暂无售后记录</p>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.limit" class="pagination">
            <button :disabled="pagination.page <= 1" @click="changePage(pagination.page - 1)" class="page_btn">
                上一页
            </button>
            <span class="page_info">
                {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.limit) }}
            </span>
            <button :disabled="pagination.page >= Math.ceil(pagination.total / pagination.limit)"
                @click="changePage(pagination.page + 1)" class="page_btn">
                下一页
            </button>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { GetRMAList } from '@api/account'

const { t } = useI18n()
const router = useRouter()

const loading = ref(false)
const aftersaleList = ref([])
const pagination = ref({
    page: 1,
    limit: 10,
    total: 0
})

// 获取售后列表
const loadAftersaleList = async () => {
    try {
        loading.value = true
        const params = {
            page: pagination.value.page,
            limit: pagination.value.limit
        }

        const response = await GetRMAList(params)
        console.log('售后列表:', response)

        aftersaleList.value = response.rmas || []
        pagination.value.total = response.total || 0
    } catch (error) {
        console.error('获取售后列表失败:', error)
        ElMessage.error('获取售后列表失败')
    } finally {
        loading.value = false
    }
}

// 获取产品图片
const getProductImage = (product) => {
    if (product && product.image) {
        return `http://47.237.73.30/${product.image}`
    }
    return 'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819'
}

// 获取类型文本
const getTypeText = (type) => {
    const typeMap = {
        'return': '退货',
        'exchange': '换货',
        'repair': '维修'
    }
    return typeMap[type] || type
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
}

// 查看详情
const viewDetail = (item) => {
    router.push(`/aftersale/detail?id=${item.id}`)
}

// 切换页码
const changePage = (page) => {
    pagination.value.page = page
    loadAftersaleList()
}

onMounted(() => {
    loadAftersaleList()
})
</script>
<style lang="scss" scoped>
@import url('./aftersale.scss');
</style>