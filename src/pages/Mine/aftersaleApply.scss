.aftersale_apply_container {
  min-height: 68.4rem;
  background: #ffffff;
  padding: 2rem;
  border-radius: 2rem;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 2.4rem;
    background: #ffffff;
    padding: 2rem;
    border-radius: 1.2rem;

    .back_btn {
      width: 2.4rem;
      height: 2.4rem;
      margin-right: 1.6rem;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2rem;
      color: #242426;
      margin: 0;
    }
  }

  .content {
    background: #ffffff;
    border-radius: 1.2rem;
    padding: 2.4rem;
  }

  .product_info {
    display: flex;
    align-items: center;
    padding: 2rem;
    border: 0.1rem solid #e7e7e7;
    border-radius: 1.2rem;
    margin-bottom: 2.4rem;

    .product_image {
      width: 8rem;
      height: 8rem;
      margin-right: 1.6rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.8rem;
      }
    }

    .product_details {
      flex: 1;

      .product_name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.6rem;
        color: #242426;
        margin: 0 0 0.8rem 0;
      }

      .product_quantity {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #70707b;
      }
    }
  }

  .form_section {
    .section_title {
      display: flex;
      align-items: center;
      margin-bottom: 2rem;

      .title_bar {
        width: 0.4rem;
        height: 1.6rem;
        background: #6e4aeb;
        border-radius: 0.2rem;
        margin-right: 1.2rem;
      }

      span {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.6rem;
        color: #242426;
      }
    }

    .form_group {
      margin-bottom: 2rem;

      &.half {
        width: 48%;
      }

      .form_label {
        display: block;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 1.4rem;
        color: #242426;
        margin-bottom: 0.8rem;

        .required {
          color: #ff4d4f;
        }
      }

      .form_control {
        .full_width {
          width: 100%;
        }
      }
    }

    .form_row {
      display: flex;
      gap: 2rem;
    }
  }

  .upload_area {
    display: flex;
    flex-wrap: wrap;
    gap: 1.2rem;

    .uploaded_images {
      display: flex;
      flex-wrap: wrap;
      gap: 1.2rem;
    }

    .image_item {
      position: relative;
      width: 8rem;
      height: 8rem;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.8rem;
        border: 0.1rem solid #e7e7e7;
      }

      .remove_btn {
        position: absolute;
        top: -0.5rem;
        right: -0.5rem;
        width: 2rem;
        height: 2rem;
        background: #ff4d4f;
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 1.2rem;
        font-weight: bold;

        &:hover {
          background: #ff7875;
        }
      }
    }

    .upload_btn {
      width: 8rem;
      height: 8rem;
      border: 0.2rem dashed #d9d9d9;
      border-radius: 0.8rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #6e4aeb;
        background: #f6f4ff;
      }

      .upload_icon {
        font-size: 2.4rem;
        color: #d9d9d9;
      }

      &:hover .upload_icon {
        color: #6e4aeb;
      }
    }
  }

  .submit_section {
    margin-top: 3rem;
    text-align: center;

    .submit_btn {
      width: 20rem;
      height: 4.8rem;
      background: #6e4aeb;
      color: #ffffff;
      border: none;
      border-radius: 2.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.6rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: #5a3dc9;
      }

      &:disabled {
        background: #d9d9d9;
        cursor: not-allowed;
      }
    }
  }

  .empty_state {
    text-align: center;
    padding: 4rem;
    background: #ffffff;
    border-radius: 1.2rem;
    color: #999;
    font-size: 1.4rem;

    .retry_btn {
      margin-top: 1rem;
      padding: 0.8rem 1.6rem;
      background: #6e4aeb;
      color: #fff;
      border-radius: 0.4rem;
      cursor: pointer;
      display: inline-block;

      &:hover {
        background: #5a3dc9;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea) {
  width: 100%;
}
