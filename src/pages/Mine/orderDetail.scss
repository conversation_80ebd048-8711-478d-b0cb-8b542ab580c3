.box {
  min-height: 69rem;
  background: #ffffff;
  border-radius: 2rem;
  padding: 3rem 2.4rem 2.4rem 5.4rem;
  box-sizing: border-box;

  // 产品卡片样式
  .product_card {
    display: flex;
    padding: 2.4rem;
    border: 0.1rem solid #e7e7e7;
    border-radius: 1.2rem;
    margin-bottom: 2.4rem;
    background: #ffffff;

    &_image {
      width: 12rem;
      height: 12rem;
      margin-right: 2.4rem;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.8rem;
      }
    }

    &_content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    &_header {
      display: flex;
      // justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.2rem;

      .product_name {
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.8rem;
        color: #242426;
        margin: 0;
        line-height: 2.4rem;
      }

      .product_card_actions {
        display: flex;
        align-items: center;
        gap: 1.2rem;

        .product_status {
          padding: 0.4rem 1.2rem;
          background: #fff4de;
          color: #e59802;
          border-radius: 1.2rem;
          font-size: 1.2rem;
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }

    .product_quantity {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #70707b;
      margin-bottom: 0.8rem;
    }

    .product_price {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.8rem;
      color: #242426;
      margin-bottom: 1.6rem;
    }

    .order_details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.8rem 2.4rem;

      .order_detail_row {
        display: flex;
        align-items: center;

        .label {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.3rem;
          color: #70707b;
          white-space: nowrap;
          margin-right: 0.8rem;
        }

        .value {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 1.3rem;
          color: #242426;
          word-break: break-all;
        }
      }
    }
  }
  &_title {
    display: flex;
    align-items: center;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3rem;
    position: relative;
    &_back {
      margin-right: 1.6rem;
      width: 2.4rem;
      height: 2.4rem;
    }
    &_action {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      align-items: center;
      &_item {
        padding: 0 1.9rem;
        box-sizing: border-box;
        height: 3.2rem;
        border-radius: 3.2rem;
        display: flex;
        align-items: center;
        border: 0.1rem solid #e7e7e7;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #242426;
        background: #f6f6f6;
        color: #242426;
      }
      &_item.active {
        background: #6e4aeb;
        border-color: #6e4aeb;
        color: #ffffff;
        margin-right: 1.6rem;
      }

      .apply_aftersale_btn {
        padding: 0.6rem 1.6rem;
        background: #f6f6f6;
        color: #242426;
        border: 0.1rem solid #e7e7e7;
        border-radius: 1.6rem;
        font-size: 1.2rem;
        font-weight: 500;
        cursor: pointer;
        white-space: nowrap;
        transition: all 0.3s ease;

        &:hover {
          background: #6e4aeb;
          color: #ffffff;
          border-color: #6e4aeb;
        }
      }
    }
  }
  &_tabs {
    display: flex;
    align-items: center;
    height: 4.6rem;
    padding-left: 2.4rem;
    box-sizing: border-box;
    background: #f6f6f6;
    margin-bottom: 1.6rem;
    &_item {
      height: 4.6rem;
      line-height: 4.6rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #70707b;
    }
  }
  &_info {
    height: 2.4rem;
    display: flex;
    align-items: center;
    padding-left: 2.4rem;
    box-sizing: border-box;
    margin-bottom: 3.2rem;
    &_item {
      height: 2.4rem;
      line-height: 2.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #242426;
    }
  }
  &_subtitle {
    height: 2.2rem;
    line-height: 100%;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.6rem;
    color: #242426;
    display: flex;
    align-items: center;
    margin-bottom: 1.6rem;
    label {
      width: 0.4rem !important;
      height: 1.4rem !important;
      background: #6e4aeb;
      border-radius: 0.2rem;
      margin-right: 1.2rem;
    }
  }
  &_address {
    display: flex;
    height: 3.6rem;
    background: #f6f6f6;
    margin-bottom: 1.6rem;
    &_item {
      height: 3.6rem;
      line-height: 3.6rem;
      flex: 1;
      padding-left: 2.4rem;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #70707b;
    }
  }
  &_addressInfo {
    margin-bottom: 3.2rem;
    display: flex;
    &_item {
      flex: 1;
      padding-left: 2.4rem;
      box-sizing: border-box;
      &_desc {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #242426;
        line-height: 2.4rem;
      }
    }
  }
  &_goods {
    padding: 0 0.2rem 0 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // background: #f6f6f6;
    margin-bottom: 3.2rem;
    &_price {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.4rem;
      color: #242426;
      flex: 1;
      padding-left: 2.4rem;
    }
    &_info {
      display: flex;
      align-items: center;
      flex: 1;
      border-bottom: 1px solid #e7e7e7;
      padding: 1.5rem 0;
      &_img {
        width: 10rem;
        height: 10rem;
        margin-right: 1.6rem;
      }
      &_name {
        height: 2.2rem;
        line-height: 2.2rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.4rem;
        color: #242426;
        flex: 1;
        padding-left: 2.4rem;
      }
      &_num {
        height: 2rem;
        line-height: 2rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #242426;
        flex: 1;
        padding-left: 2.4rem;
      }
    }
  }
  &_note {
    margin-bottom: 3.2rem;
    background: #f6f6f6;
    padding: 2.4rem 2.4rem 3.4rem 2.4rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.6rem;
    color: #242426;
    line-height: 2.2rem;
  }
  &_detail {
    margin-bottom: 3.2rem;
    padding-left: 2rem;
    p {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.6rem;
      color: #242426;
      line-height: 3rem;
    }
  }
  &_line {
    width: 100%;
    height: 0px;
    border: 0.1rem solid #e7e7e7;
    margin-bottom: 2rem;
  }
  &_total {
    display: flex;
    align-items: center;
    height: 2.8rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 2rem;
    color: #242426;
    margin-bottom: 3.2rem;
    span {
      color: #c33232;
      font-weight: 600;
    }
  }
  &_action {
    display: flex;
    align-items: center;
    &_item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.4rem;
      color: #242426;
      border-radius: 3.2rem;
      background: #f6f6f6;
      box-sizing: border-box;
      cursor: pointer;
    }
    &_item.cancel {
      height: 4.8rem;
      padding: 0 2.4rem;
      border: 0.1rem solid #e7e7e7;
    }
    &_item.pay {
      height: 4.8rem;
      padding: 0 7.3rem;
      background: #6e4aeb;
      color: #ffffff;
      margin-right: 1.2rem;
    }
  }
}
.flex {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 1.1rem;
}
.detail {
  margin-left: 1.6rem;
  display: flex;
  flex-direction: column;
  &_name {
    height: 2.2rem;
    line-height: 2.2rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 1.6rem;
    color: #242426;
  }
  &_num {
    height: 2rem;
    line-height: 2rem;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.4rem;
    color: #242426;
  }
}
.w383 {
  width: 38.3rem;
}
.w122 {
  width: 12.2rem;
}
.w200 {
  width: 20rem;
}
.flex1 {
  flex: 1;
}
.pay {
  padding: 0 2rem;
  height: 3.2rem;
  line-height: 3.2rem;
  background: #6e4aeb;
  border-radius: 3.2rem;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 1.4rem;
  color: #ffffff;
  cursor: pointer;
}
.info {
  cursor: pointer;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 1.4rem;
  color: #70707b;
}
.w180 {
  width: 18rem;
}
.w214 {
  width: 21.4rem;
}
.w97 {
  width: 9.7rem;
}
.w219 {
  width: 21.9rem;
}
.w137 {
  width: 13.7rem;
}
.w109 {
  width: 10.9rem;
}
