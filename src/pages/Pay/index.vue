<template>
    <div class="pay_box">
        <div class="pay_box_main">
            <!-- 订单信息 -->
            <div class="order-info">
                <h2 class="order-title">{{ $t('payment.orderSubmitted', '订单提交成功，请付款') }}</h2>
                <div class="order-details">
                    <div class="order-item">
                        <span class="label">{{ $t('payment.orderNumber', '订单号') }}：</span>
                        <span class="value">{{ orderInfo.orderNumber || '123456789' }}</span>
                    </div>
                    <div class="order-item">
                        <span class="label">{{ $t('payment.amount', '支付金额') }}：</span>
                        <span class="value amount">{{ formatAmount(orderInfo.amount || 100) }}</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-methods">
                <h3 class="section-title">{{ $t('payment.selectMethod', '选择支付方式') }}</h3>
                <el-radio-group v-model="selectedPaymentMethod" class="payment-options">
                    <el-radio value="stripe" class="payment-option">
                        <div class="payment-option-content">
                            <img src="/images/stripe-logo.png" alt="Stripe" class="payment-logo" />
                            <span>{{ $t('payment.stripe.name', 'Stripe 支付') }}</span>
                        </div>
                    </el-radio>
                    <el-radio value="paypal" class="payment-option">
                        <div class="payment-option-content">
                            <img src="/images/paypal-logo.png" alt="PayPal" class="payment-logo" />
                            <span>{{ $t('payment.paypal.name', 'PayPal 支付') }}</span>
                        </div>
                    </el-radio>
                    <el-radio value="wechat" class="payment-option">
                        <div class="payment-option-content">
                            <img src="/images/wechat-logo.png" alt="微信支付" class="payment-logo" />
                            <span>{{ $t('payment.wechat.name', '微信支付') }}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </div>

            <!-- Stripe 支付按钮 -->
            <div v-if="selectedPaymentMethod === 'stripe'" class="payment-form-container">
                <div class="stripe-payment-section">
                    <div class="payment-info">
                        <div class="info-item">
                            <span class="label">支付方式：</span>
                            <span class="value">Stripe 信用卡支付</span>
                        </div>
                        <div class="info-item">
                            <span class="label">支持卡种：</span>
                            <span class="value">Visa、MasterCard、American Express</span>
                        </div>
                    </div>
                    <el-button type="primary" size="large" @click="openStripeDialog" class="stripe-pay-button">
                        <el-icon>
                            <CreditCard />
                        </el-icon>
                        使用信用卡支付
                    </el-button>
                </div>
            </div>

            <!-- Stripe 支付弹窗 -->
            <StripePaymentForm v-model:visible="stripeDialogVisible" :publishable-key="stripeConfig.publishableKey"
                :amount="orderInfo.amount" :order-number="orderInfo.orderNumber" :currency="orderInfo.currency || 'usd'"
                @success="handlePaymentSuccess" @error="handlePaymentError" @cancel="handlePaymentCancel" />

            <!-- PayPal 支付容器 -->
            <div v-if="selectedPaymentMethod === 'paypal'" class="payment-form-container">
                <div id="paypal-button-container"></div>
            </div>

            <!-- 微信支付二维码 -->
            <div v-if="selectedPaymentMethod === 'wechat'" class="payment-form-container">
                <div class="wechat-payment">
                    <div class="qr-code-container">
                        <div id="wechat-qr-code"></div>
                    </div>
                    <p class="wechat-tip">{{ $t('payment.wechat.scanTip', '请使用微信扫描二维码完成支付') }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { CreditCard } from '@element-plus/icons-vue'
import StripePaymentForm from '@/components/StripePaymentForm.vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const selectedPaymentMethod = ref('stripe')
const stripeDialogVisible = ref(false)

// 订单信息
const orderInfo = reactive({
    orderNumber: route.query.orderNumber || '123456789',
    amount: parseFloat(route.query.amount) || 100.00,
    currency: route.query.currency || 'usd'
})

// 支付配置
const stripeConfig = reactive({
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_51234567890abcdef'
})

// 格式化金额显示
const formatAmount = computed(() => {
    return (amount) => {
        const currency = orderInfo.currency.toUpperCase()
        if (currency === 'USD') {
            return `$${amount.toFixed(2)}`
        } else if (currency === 'CNY') {
            return `¥${amount.toFixed(2)}`
        }
        return `${currency} ${amount.toFixed(2)}`
    }
})

// 打开 Stripe 支付弹窗
const openStripeDialog = () => {
    if (!stripeConfig.publishableKey || stripeConfig.publishableKey === 'pk_test_51234567890abcdef') {
        ElMessage.error('请先配置 Stripe 公钥')
        return
    }
    stripeDialogVisible.value = true
}

// 处理支付成功
const handlePaymentSuccess = (result) => {
    console.log('支付成功:', result)
    ElMessage.success('支付成功！')
    stripeDialogVisible.value = false

    // 跳转到支付成功页面
    router.push({
        path: '/payment/success',
        query: {
            orderNumber: result.orderNumber,
            paymentMethod: selectedPaymentMethod.value
        }
    })
}

// 处理支付错误
const handlePaymentError = (error) => {
    console.error('支付失败:', error)
    ElMessage.error(error.message || '支付失败，请重试')
}

// 处理支付取消
const handlePaymentCancel = () => {
    console.log('用户取消支付')
    stripeDialogVisible.value = false
}

// 初始化其他支付方式
const initOtherPayments = async () => {
    // 这里可以根据需要初始化 PayPal 和微信支付
    // 暂时留空，等待具体需求
}

onMounted(() => {
    initOtherPayments()
})
</script>

<style lang="scss" scoped>
@import url('./index.scss');
</style>