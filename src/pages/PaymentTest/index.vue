<template>
  <div class="payment-test">
    <div class="test-container">
      <h1 class="test-title">Stripe 支付测试页面</h1>

      <!-- 测试配置 -->
      <div class="test-config">
        <h3>测试配置</h3>
        <el-form :model="testConfig" label-width="120px">
          <el-form-item label="Stripe 公钥">
            <el-input v-model="testConfig.publishableKey" placeholder="pk_test_..." type="textarea" :rows="2" />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="testConfig.orderNumber" placeholder="TEST123456" />
          </el-form-item>
          <el-form-item label="支付金额">
            <el-input-number v-model="testConfig.amount" :min="0.01" :step="0.01" :precision="2" />
          </el-form-item>
          <el-form-item label="货币">
            <el-select v-model="testConfig.currency">
              <el-option label="USD" value="usd" />
              <el-option label="CNY" value="cny" />
              <el-option label="EUR" value="eur" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 测试卡号信息 -->
      <div class="test-cards">
        <h3>测试卡号</h3>
        <div class="card-list">
          <div class="card-item">
            <strong>成功支付：</strong>4242 4242 4242 4242
          </div>
          <div class="card-item">
            <strong>需要验证：</strong>4000 0025 0000 3155
          </div>
          <div class="card-item">
            <strong>被拒绝：</strong>4000 0000 0000 0002
          </div>
          <div class="card-item">
            <strong>有效期：</strong>任何未来日期（如：12/25）
          </div>
          <div class="card-item">
            <strong>CVV：</strong>任何3位数字（如：123）
          </div>
        </div>
      </div>

      <!-- Stripe 支付按钮 -->
      <div class="payment-form" v-if="testConfig.publishableKey">
        <h3>支付测试</h3>
        <el-button type="primary" size="large" @click="openStripeDialog" class="test-pay-button">
          <el-icon>
            <CreditCard />
          </el-icon>
          测试 Stripe 支付
        </el-button>
      </div>

      <!-- 提示信息 -->
      <div class="tips" v-else>
        <el-alert title="请先配置 Stripe 公钥" description="在上方输入你的 Stripe 测试环境公钥（以 pk_test_ 开头）" type="warning" show-icon
          :closable="false" />
      </div>

      <!-- Stripe 支付弹窗 -->
      <StripePaymentForm v-model:visible="stripeDialogVisible" :publishable-key="testConfig.publishableKey"
        :amount="testConfig.amount" :order-number="testConfig.orderNumber" :currency="testConfig.currency"
        @success="handlePaymentSuccess" @error="handlePaymentError" @cancel="handlePaymentCancel" />

      <!-- 测试结果 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <div class="results-list">
          <div v-for="(result, index) in testResults" :key="index" :class="['result-item', result.type]">
            <div class="result-time">{{ result.time }}</div>
            <div class="result-type">{{ result.type === 'success' ? '成功' : '失败' }}</div>
            <div class="result-message">{{ result.message }}</div>
            <div class="result-data" v-if="result.data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { CreditCard } from '@element-plus/icons-vue'
import StripePaymentForm from '@/components/StripePaymentForm.vue'
import moment from 'moment'

// 测试配置
const testConfig = reactive({
  publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
  orderNumber: 'TEST' + Date.now(),
  amount: 10.00,
  currency: 'usd'
})

// 弹窗控制
const stripeDialogVisible = ref(false)

// 测试结果
const testResults = ref([])

// 打开支付弹窗
const openStripeDialog = () => {
  stripeDialogVisible.value = true
}

// 处理支付成功
const handlePaymentSuccess = (result) => {
  console.log('支付成功:', result)
  ElMessage.success('支付测试成功！')

  testResults.value.unshift({
    type: 'success',
    time: moment().format('HH:mm:ss'),
    message: '支付成功',
    data: result
  })
}

// 处理支付错误
const handlePaymentError = (error) => {
  console.error('支付失败:', error)
  ElMessage.error('支付测试失败：' + error.message)

  testResults.value.unshift({
    type: 'error',
    time: moment().format('HH:mm:ss'),
    message: error.message || '支付失败',
    data: error
  })
}

// 处理支付取消
const handlePaymentCancel = () => {
  console.log('用户取消支付')
  ElMessage.info('已取消支付')
}
</script>

<style lang="scss" scoped>
.payment-test {
  min-height: calc(100vh - 250px);
  padding: 2rem;
  background: #f8f9fa;

  .test-container {
    max-width: 80rem;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.1);

    .test-title {
      font-size: 2.4rem;
      font-weight: 600;
      color: #242426;
      text-align: center;
      margin-bottom: 3rem;
    }

    .test-config,
    .test-cards,
    .payment-form,
    .test-results {
      margin-bottom: 3rem;
      padding: 2rem;
      border: 0.1rem solid #e7e7e7;
      border-radius: 0.8rem;
      background: #fafafa;

      h3 {
        font-size: 1.8rem;
        font-weight: 600;
        color: #242426;
        margin-bottom: 1.5rem;
      }
    }

    .test-cards {
      .card-list {
        .card-item {
          padding: 0.8rem 0;
          font-size: 1.4rem;
          color: #606266;
          border-bottom: 0.1rem solid #e7e7e7;

          &:last-child {
            border-bottom: none;
          }

          strong {
            color: #242426;
          }
        }
      }
    }

    .tips {
      margin-bottom: 3rem;
    }

    .test-pay-button {
      width: 100%;
      max-width: 30rem;
      height: 4.8rem;
      font-size: 1.6rem;
      font-weight: 600;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        margin-right: 0.8rem;
      }
    }

    .test-results {
      .results-list {
        .result-item {
          padding: 1.5rem;
          margin-bottom: 1rem;
          border-radius: 0.6rem;
          border-left: 0.4rem solid;

          &.success {
            background: #f0f9ff;
            border-left-color: #67c23a;
          }

          &.error {
            background: #fef0f0;
            border-left-color: #f56c6c;
          }

          .result-time {
            font-size: 1.2rem;
            color: #909399;
            margin-bottom: 0.5rem;
          }

          .result-type {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 0.5rem;

            .success & {
              color: #67c23a;
            }

            .error & {
              color: #f56c6c;
            }
          }

          .result-message {
            font-size: 1.4rem;
            color: #242426;
            margin-bottom: 1rem;
          }

          .result-data {
            background: #f5f5f5;
            border-radius: 0.4rem;
            padding: 1rem;
            overflow-x: auto;

            pre {
              margin: 0;
              font-size: 1.2rem;
              color: #606266;
              white-space: pre-wrap;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .payment-test {
    padding: 1rem;

    .test-container {
      padding: 2rem 1.5rem;

      .test-title {
        font-size: 2rem;
      }

      .test-config,
      .test-cards,
      .payment-form,
      .test-results {
        padding: 1.5rem;
      }
    }
  }
}
</style>
