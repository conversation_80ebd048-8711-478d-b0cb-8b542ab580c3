import { post } from '@utils/request';

/**
 * PayPal 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CatchreatePayPalPayment = (paymentData = {}) => {
  return post('/paypal/create', paymentData);
};

/**
 * PayPal 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CapturePayPalPayment = (captureData = {}) => {
  return post('/paypal/capture', captureData);
};

/**
 * Stripe 创建支付意图
 * @param {Object} paymentData - 支付数据
 * @param {string} paymentData.amount - 支付金额
 * @param {string} paymentData.currency - 货币类型
 * @param {string} paymentData.order_number - 订单号
 * @returns {Promise}
 */
export const CreateStripePaymentIntent = (paymentData = {}) => {
  return post('/stripe/create-payment-intent', paymentData);
};

/**
 * Stripe 处理支付（使用 Token）
 * @param {Object} captureData - 支付数据
 * @param {string} captureData.token - Stripe Token
 * @param {string} captureData.order_number - 订单号
 * @param {number} captureData.amount - 支付金额
 * @param {string} captureData.currency - 货币类型
 * @returns {Promise}
 */
export const CaptureStripePayment = (captureData = {}) => {
  return post('/stripe/capture', captureData);
};

/**
 * Stripe 创建支付（兼容旧版本）
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateStripePayment = (paymentData = {}) => {
  return post('/stripe/create', paymentData);
};

/**
 * BitPay 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateBitPayPayment = (paymentData = {}) => {
  return post('/bitpay/create', paymentData);
};

/**
 * BitPay 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CaptureBitPayPayment = (captureData = {}) => {
  return post('/bitpay/capture', captureData);
};

/**
 * 微信H5 创建支付
 * @param {Object} paymentData - 支付数据
 * @returns {Promise}
 */
export const CreateWeChatH5Payment = (paymentData = {}) => {
  return post('/wechat_h5/create', paymentData);
};

/**
 * 微信H5 捕获支付
 * @param {Object} captureData - 捕获数据
 * @returns {Promise}
 */
export const CaptureWeChatH5Payment = (captureData = {}) => {
  return post('/wechat_h5/capture', captureData);
};
