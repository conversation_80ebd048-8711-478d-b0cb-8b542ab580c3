<template>
  <el-dialog v-model="dialogVisible" title="Stripe 信用卡支付" width="600px" :before-close="handleClose"
    :close-on-click-modal="false" destroy-on-close>
    <div class="stripe-payment-dialog" v-loading="loading">
      <!-- 订单信息 -->
      <div class="order-summary">
        <div class="summary-row">
          <span class="label">订单号：</span>
          <span class="value">{{ orderNumber }}</span>
        </div>
        <div class="summary-row">
          <span class="label">支付金额：</span>
          <span class="value amount">${{ amount }}</span>
        </div>
      </div>

      <!-- 支付表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" class="stripe-form">
        <!-- 持卡人姓名 -->
        <el-form-item label="持卡人姓名" prop="cardholderName" class="form-item">
          <el-input v-model="form.cardholderName" placeholder="请输入持卡人姓名" size="large"
            :class="{ 'error-input': errors.cardholderName }" @input="validateCardholderName" />
          <div v-if="errors.cardholderName" class="error-message">
            {{ errors.cardholderName }}
          </div>
        </el-form-item>

        <!-- 信用卡号 -->
        <el-form-item label="信用卡号" class="form-item">
          <div id="stripe-card-number-element" :class="['stripe-element', { 'error-element': errors.cardNumber }]">
          </div>
          <div v-if="errors.cardNumber" class="error-message">
            {{ errors.cardNumber }}
          </div>
        </el-form-item>

        <!-- 有效期和CVV -->
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="有效期" class="form-item">
              <div id="stripe-card-expiry-element" :class="['stripe-element', { 'error-element': errors.cardExpiry }]">
              </div>
              <div v-if="errors.cardExpiry" class="error-message">
                {{ errors.cardExpiry }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CVV" class="form-item">
              <div id="stripe-card-cvc-element" :class="['stripe-element', { 'error-element': errors.cardCvc }]"></div>
              <div v-if="errors.cardCvc" class="error-message">
                {{ errors.cardCvc }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 测试卡号提示 -->
      <div class="test-cards-tip">
        <el-alert title="测试卡号" type="info" :closable="false" show-icon>
          <template #default>
            <div class="test-cards">
              <div>成功支付：4242 4242 4242 4242</div>
              <div>需要验证：4000 0025 0000 3155</div>
              <div>被拒绝：4000 0000 0000 0002</div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 安全提示 -->
      <div class="security-notice">
        <el-icon>
          <Lock />
        </el-icon>
        <span>您的支付信息通过SSL加密传输，安全可靠</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">取消</el-button>
        <el-button type="primary" size="large" :loading="processing" @click="handlePayment">
          {{ processing ? '处理中...' : `支付 $${amount}` }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import { StripePayment } from '@/utils/payment'
import { CaptureStripePayment } from '@/api/payment'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  publishableKey: {
    type: String,
    required: true
  },
  amount: {
    type: [Number, String],
    required: true
  },
  orderNumber: {
    type: String,
    required: true
  },
  currency: {
    type: String,
    default: 'usd'
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success', 'error', 'cancel'])

// Refs
const formRef = ref()
const dialogVisible = ref(props.visible)
const loading = ref(false)
const processing = ref(false)

// 表单数据
const form = reactive({
  cardholderName: ''
})

// 错误信息
const errors = reactive({
  cardholderName: '',
  cardNumber: '',
  cardExpiry: '',
  cardCvc: ''
})

// 表单验证规则
const rules = {
  cardholderName: [
    { required: true, message: '请输入持卡人姓名', trigger: 'blur' }
  ]
}

// Stripe 实例
let stripePayment = null

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 弹窗打开时初始化 Stripe
    nextTick(() => {
      initStripe()
    })
  }
})

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal && stripePayment) {
    // 弹窗关闭时清理 Stripe 实例
    stripePayment.destroy()
    stripePayment = null
  }
})

// 初始化 Stripe
const initStripe = async () => {
  try {
    loading.value = true

    stripePayment = new StripePayment({
      publishableKey: props.publishableKey
    })

    await stripePayment.initSDK()

    await nextTick()

    // 创建并挂载 Stripe 元素（使用唯一ID避免冲突）
    stripePayment.createAndMountFormElements()
    stripePayment.mountElements({
      cardNumber: '#stripe-card-number-element',
      cardExpiry: '#stripe-card-expiry-element',
      cardCvc: '#stripe-card-cvc-element'
    })

    // 监听 Stripe 元素变化
    setupStripeEventListeners()

  } catch (error) {
    console.error('Stripe 初始化失败:', error)
    ElMessage.error('支付系统初始化失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

// 设置 Stripe 事件监听
const setupStripeEventListeners = () => {
  if (!stripePayment) return

  // 监听卡号变化
  stripePayment.cardNumberElement.on('change', (event) => {
    errors.cardNumber = event.error ? event.error.message : ''
  })

  // 监听有效期变化
  stripePayment.cardExpiryElement.on('change', (event) => {
    errors.cardExpiry = event.error ? event.error.message : ''
  })

  // 监听CVV变化
  stripePayment.cardCvcElement.on('change', (event) => {
    errors.cardCvc = event.error ? event.error.message : ''
  })
}

// 验证持卡人姓名
const validateCardholderName = () => {
  if (!form.cardholderName.trim()) {
    errors.cardholderName = '请输入持卡人姓名'
  } else {
    errors.cardholderName = ''
  }
}

// 处理支付
const handlePayment = async () => {
  try {
    // 验证表单
    await formRef.value.validate()

    // 验证持卡人姓名
    validateCardholderName()

    // 检查是否有错误
    if (Object.values(errors).some(error => error)) {
      ElMessage.error('请检查并修正表单错误')
      return
    }

    processing.value = true

    // 创建 Token
    const token = await stripePayment.createToken(form.cardholderName)

    // 调用后端接口处理支付
    const result = await CaptureStripePayment({
      token: token.id,
      order_number: props.orderNumber,
      amount: props.amount,
      currency: props.currency
    })

    if (result.status === 'success') {
      ElMessage.success('支付成功！')
      dialogVisible.value = false
      emit('success', {
        token: token.id,
        orderNumber: props.orderNumber,
        result
      })
    } else {
      throw new Error(result.message || '支付失败')
    }

  } catch (error) {
    console.error('支付处理失败:', error)
    ElMessage.error(error.message || '支付处理失败，请重试')
    emit('error', error)
  } finally {
    processing.value = false
  }
}

// 处理弹窗关闭
const handleClose = () => {
  dialogVisible.value = false
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.stripe-payment-dialog {
  .order-summary {
    background: #f8f9fa;
    border-radius: 0.8rem;
    padding: 1.5rem;
    margin-bottom: 2rem;

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.8rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 1.4rem;
        color: #606266;
      }

      .value {
        font-size: 1.4rem;
        font-weight: 500;
        color: #242426;

        &.amount {
          font-size: 1.8rem;
          font-weight: 600;
          color: #409eff;
        }
      }
    }
  }

  .stripe-form {
    .form-item {
      margin-bottom: 2rem;

      :deep(.el-form-item__label) {
        font-size: 1.4rem;
        font-weight: 500;
        color: #242426;
        margin-bottom: 0.8rem;
      }

      .stripe-element {
        height: 4rem;
        padding: 0 1.2rem;
        border: 0.1rem solid #dcdfe6;
        border-radius: 0.4rem;
        background: #ffffff;
        transition: border-color 0.3s;

        &:hover {
          border-color: #c0c4cc;
        }

        &:focus-within {
          border-color: #409eff;
        }

        &.error-element {
          border-color: #f56c6c;
        }
      }

      .error-input {
        :deep(.el-input__wrapper) {
          border-color: #f56c6c;
        }
      }

      .error-message {
        color: #f56c6c;
        font-size: 1.2rem;
        margin-top: 0.4rem;
      }
    }
  }

  .test-cards-tip {
    margin: 2rem 0;

    .test-cards {
      font-size: 1.2rem;
      line-height: 1.6;

      div {
        margin-bottom: 0.4rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .security-notice {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    color: #909399;
    font-size: 1.3rem;
    text-align: center;
    margin-top: 1.5rem;

    .el-icon {
      color: #67c23a;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

// 自定义弹窗样式
:deep(.el-dialog__header) {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #ebeef5;

  .el-dialog__title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #303133;
  }
}

:deep(.el-dialog__body) {
  padding: 2rem;
}

:deep(.el-dialog__footer) {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #ebeef5;
}
</style>
