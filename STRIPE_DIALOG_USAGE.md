# Stripe 弹窗支付使用指南

## 概述

我已经重新实现了 Stripe 支付功能，采用弹窗（Dialog）的方式，使用更加简洁和用户友好。新的实现包括：

- 🎯 **弹窗式支付表单**：点击按钮弹出支付窗口
- 🔒 **安全可靠**：使用 Stripe Elements 确保 PCI 合规
- 📱 **响应式设计**：适配桌面和移动设备
- 🎨 **统一设计风格**：符合项目 Element Plus 设计系统
- 🧪 **内置测试卡号**：方便开发测试

## 快速开始

### 1. 配置环境变量

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，添加你的 Stripe 公钥
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

### 2. 访问演示页面

启动开发服务器后，访问以下页面：

- **演示页面**：`http://localhost:5173/stripe/demo`
- **支付页面**：`http://localhost:5173/pay`
- **测试页面**：`http://localhost:5173/payment/test`

## 组件使用

### 基本用法

```vue
<template>
  <div>
    <!-- 支付按钮 -->
    <el-button @click="openPayment">支付</el-button>
    
    <!-- Stripe 支付弹窗 -->
    <StripePaymentForm
      v-model:visible="paymentVisible"
      :publishable-key="stripeKey"
      :amount="100.00"
      :order-number="orderNumber"
      :currency="'usd'"
      @success="handleSuccess"
      @error="handleError"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StripePaymentForm from '@/components/StripePaymentForm.vue'

const paymentVisible = ref(false)
const stripeKey = 'pk_test_your_key_here'
const orderNumber = 'ORDER123456'

const openPayment = () => {
  paymentVisible.value = true
}

const handleSuccess = (result) => {
  console.log('支付成功:', result)
  // 处理支付成功逻辑
}

const handleError = (error) => {
  console.error('支付失败:', error)
  // 处理支付失败逻辑
}

const handleCancel = () => {
  console.log('用户取消支付')
  // 处理取消逻辑
}
</script>
```

### 组件属性

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `v-model:visible` | Boolean | 是 | false | 控制弹窗显示/隐藏 |
| `publishable-key` | String | 是 | - | Stripe 公钥 |
| `amount` | Number/String | 是 | - | 支付金额 |
| `order-number` | String | 是 | - | 订单号 |
| `currency` | String | 否 | 'usd' | 货币类型 |

### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `success` | `{ token, orderNumber, result }` | 支付成功时触发 |
| `error` | `error` | 支付失败时触发 |
| `cancel` | - | 用户取消支付时触发 |

## 测试卡号

在开发环境中，可以使用以下测试卡号：

| 卡号 | 结果 | 说明 |
|------|------|------|
| `4242 4242 4242 4242` | 成功 | 标准测试卡 |
| `4000 0025 0000 3155` | 需要验证 | 需要 3D Secure 验证 |
| `4000 0000 0000 0002` | 被拒绝 | 卡被拒绝 |

**其他测试信息：**
- **有效期**：任何未来日期（如：12/25）
- **CVV**：任何3位数字（如：123）
- **持卡人姓名**：任何名称

## 功能特点

### 1. 弹窗式设计
- 点击支付按钮弹出支付窗口
- 不影响主页面布局
- 支持 ESC 键关闭
- 点击遮罩层不会关闭（防止误操作）

### 2. 实时表单验证
- 持卡人姓名必填验证
- Stripe Elements 实时卡号验证
- 有效期和 CVV 格式验证
- 错误信息实时显示

### 3. 用户体验优化
- 加载状态指示
- 支付处理中状态
- 成功/失败消息提示
- 测试卡号提示

### 4. 安全性
- 使用 Stripe Elements 确保 PCI 合规
- 敏感信息不经过你的服务器
- SSL 加密传输
- Token 化支付

## 样式定制

组件使用了项目的设计系统，如需自定义样式：

```scss
// 自定义弹窗样式
:deep(.el-dialog) {
  .el-dialog__header {
    background: your-color;
  }
}

// 自定义支付按钮样式
.stripe-pay-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}
```

## 错误处理

常见错误及解决方案：

### 1. "Stripe SDK加载失败"
- 检查网络连接
- 确认 Stripe 服务可访问
- 检查防火墙设置

### 2. "支付系统初始化失败"
- 检查 Stripe 公钥是否正确
- 确认公钥格式（以 `pk_test_` 或 `pk_live_` 开头）
- 检查环境变量配置

### 3. "请检查并修正表单错误"
- 确认持卡人姓名已填写
- 检查卡号格式是否正确
- 确认有效期和 CVV 格式

## 后端集成

确保后端实现了支付处理接口：

```javascript
// POST /api/stripe/capture
{
  "token": "tok_1234567890",
  "order_number": "ORDER123456",
  "amount": 100.00,
  "currency": "usd"
}

// 响应格式
{
  "status": "success",
  "message": "支付成功",
  "data": {
    "charge_id": "ch_1234567890",
    "amount": 100.00,
    "currency": "usd"
  }
}
```

## 开发调试

### 1. 启用调试模式
在开发环境中，组件会输出详细的调试信息到浏览器控制台。

### 2. 查看网络请求
使用浏览器开发者工具查看：
- Stripe SDK 加载请求
- Token 创建请求
- 后端支付处理请求

### 3. 错误日志
所有错误都会记录到控制台，包括：
- Stripe 初始化错误
- 表单验证错误
- 支付处理错误

## 生产环境部署

### 1. 更换生产密钥
```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_key_here
```

### 2. 启用 HTTPS
生产环境必须使用 HTTPS 协议。

### 3. 配置 Webhook
设置 Stripe Webhook 接收支付状态更新。

## 更多资源

- [Stripe 官方文档](https://stripe.com/docs)
- [Element Plus 组件库](https://element-plus.org/)
- [Vue 3 官方文档](https://vuejs.org/)

---

如有问题，请查看浏览器控制台的错误信息，或参考项目中的演示页面。
