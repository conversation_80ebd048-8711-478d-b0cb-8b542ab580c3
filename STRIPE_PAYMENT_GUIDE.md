# Stripe 支付集成指南

## 概述

本项目已集成完整的 Stripe 支付功能，包括：
- 完整的支付表单组件
- 符合项目设计风格的 UI
- 多种支付方式支持（Stripe、PayPal、微信支付）
- 支付成功页面
- 响应式设计

## 文件结构

```
src/
├── components/
│   └── StripePaymentForm.vue      # Stripe 支付表单组件
├── pages/
│   ├── Pay/
│   │   ├── index.vue              # 支付页面
│   │   └── index.scss             # 支付页面样式
│   └── PaymentSuccess/
│       └── index.vue              # 支付成功页面
├── utils/
│   └── payment.js                 # 支付工具类（已更新）
└── api/
    └── payment.js                 # 支付 API 接口
```

## 配置说明

### 1. 环境变量配置

复制 `.env.example` 文件为 `.env`，并配置你的 Stripe 公钥：

```bash
cp .env.example .env
```

在 `.env` 文件中设置：

```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

### 2. Stripe 账户设置

1. 注册 [Stripe 账户](https://stripe.com)
2. 获取测试环境的公钥（Publishable Key）
3. 配置 Webhook 端点（用于接收支付状态通知）

## 使用方法

### 1. 基本使用

在支付页面中，组件会自动根据路由参数获取订单信息：

```javascript
// 跳转到支付页面
router.push({
  path: '/pay',
  query: {
    orderNumber: '123456789',
    amount: 100.00,
    currency: 'usd'
  }
})
```

### 2. 直接使用 StripePaymentForm 组件

```vue
<template>
  <StripePaymentForm
    :publishable-key="stripePublishableKey"
    :amount="100.00"
    :order-number="orderNumber"
    :currency="'usd'"
    @success="handlePaymentSuccess"
    @error="handlePaymentError"
  />
</template>

<script setup>
import StripePaymentForm from '@/components/StripePaymentForm.vue'

const handlePaymentSuccess = (result) => {
  console.log('支付成功:', result)
  // 处理支付成功逻辑
}

const handlePaymentError = (error) => {
  console.error('支付失败:', error)
  // 处理支付失败逻辑
}
</script>
```

### 3. 使用支付工具类

```javascript
import { StripePayment } from '@/utils/payment'

const stripePayment = new StripePayment({
  publishableKey: 'pk_test_...'
})

// 初始化 SDK
await stripePayment.initSDK()

// 创建表单元素
stripePayment.createAndMountFormElements()
stripePayment.mountElements({
  cardNumber: '#card-number-element',
  cardExpiry: '#card-expiry-element',
  cardCvc: '#card-cvc-element'
})

// 创建支付 Token
const token = await stripePayment.createToken('持卡人姓名')
```

## 组件属性

### StripePaymentForm 组件

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| publishable-key | String | 是 | - | Stripe 公钥 |
| amount | Number/String | 是 | - | 支付金额 |
| order-number | String | 是 | - | 订单号 |
| currency | String | 否 | 'usd' | 货币类型 |

### 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| success | result | 支付成功时触发 |
| error | error | 支付失败时触发 |

## 样式定制

组件使用了项目的设计系统，包括：
- PingFang SC 字体
- Element Plus 组件库
- 响应式设计
- 统一的颜色方案

如需自定义样式，可以通过 CSS 变量或覆盖样式类来实现。

## 安全注意事项

1. **永远不要在前端存储私钥**：只使用 Stripe 的公钥（Publishable Key）
2. **使用 HTTPS**：生产环境必须使用 HTTPS
3. **验证支付结果**：在后端验证支付状态，不要仅依赖前端返回
4. **设置 Webhook**：使用 Stripe Webhook 接收支付状态更新

## 测试

### 测试卡号

Stripe 提供了测试卡号用于开发测试：

- **成功支付**：4242 4242 4242 4242
- **需要验证**：4000 0025 0000 3155
- **被拒绝**：4000 0000 0000 0002

### 测试信息

- **有效期**：任何未来日期
- **CVV**：任何3位数字
- **邮编**：任何5位数字

## 故障排除

### 常见问题

1. **Stripe SDK 加载失败**
   - 检查网络连接
   - 确认 Stripe 服务可访问

2. **支付表单不显示**
   - 检查公钥是否正确
   - 查看浏览器控制台错误信息

3. **支付失败**
   - 检查卡号信息是否正确
   - 确认后端接口是否正常

### 调试模式

在开发环境中，组件会输出详细的调试信息到浏览器控制台。

## 后端集成

确保后端实现了以下接口：

```javascript
// 处理 Stripe 支付
POST /api/stripe/capture
{
  "token": "tok_...",
  "order_number": "123456789",
  "amount": 100.00,
  "currency": "usd"
}
```

返回格式：
```javascript
{
  "status": "success",
  "message": "支付成功",
  "data": {
    // 支付详情
  }
}
```

## 更多资源

- [Stripe 官方文档](https://stripe.com/docs)
- [Stripe Elements 指南](https://stripe.com/docs/stripe-js)
- [Vue.js 集成示例](https://stripe.com/docs/stripe-js/vue)
